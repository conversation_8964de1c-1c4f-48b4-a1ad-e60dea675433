import { catchServiceError } from '@dbd/zod-types';
import { BadRequestError, NotFoundError } from '@dbd/zod-types-common';
import { DeliveryConfigurationService } from '@notifications/lib';
import { deliveryConfigurationContract } from '@notifications/rest-contracts';
import {
  CreateDeliveryConfigurationSchema,
  PartnerDeliveryConfigurationSchema,
  TenantDeliveryConfigurationSchema,
} from '@notifications/types';
import { initServer } from '@ts-rest/fastify';

import { AuthenticatedRequest } from './types.js';

const tsRestServer = initServer();

export class DeliveryConfigurationsController {
  private readonly deliveryConfigurationService: DeliveryConfigurationService;

  constructor(deliveryConfigurationService: DeliveryConfigurationService) {
    this.deliveryConfigurationService = deliveryConfigurationService;
  }
  /**
   * Create a new delivery configuration
   */
  createDeliveryConfiguration = tsRestServer.route(
    deliveryConfigurationContract.createDeliveryConfiguration,
    async ({ body, request }) => {
      const partnerId: string | undefined = (request as AuthenticatedRequest).user?.context.partnerId;
      const tenantId: string | undefined = (request as AuthenticatedRequest).user?.context.tenantId;
      const newConfig: CreateDeliveryConfigurationSchema = {
        ...body,
        ...(partnerId ? { partnerId } : {}),
        ...(!partnerId && tenantId ? { tenantId } : {}),
      };
      const result = await this.deliveryConfigurationService.createConfiguration(newConfig, {
        tenantId,
        partnerId,
      });

      // discriminate between tenant and partner configurations, need to figure out why ts-rest can't figure this out already based on the schema
      if (result.configurationType === 'tenant' && result.tenantId) {
        const tenantConfig: TenantDeliveryConfigurationSchema = {
          ...result,
          configurationType: 'tenant' as const,
          tenantId: result.tenantId,
        };
        return {
          status: 200,
          body: tenantConfig,
        };
      }

      if (result.configurationType === 'partner' && result.partnerId) {
        const partnerConfig: PartnerDeliveryConfigurationSchema = {
          ...result,
          configurationType: 'partner' as const,
          partnerId: result.partnerId,
        };
        return {
          status: 200,
          body: partnerConfig,
        };
      }
      return catchServiceError(new Error('Invalid configuration type'), request.log);
    },
  );

  /**
   * Update an existing delivery configuration
   */
  updateDeliveryConfiguration = tsRestServer.route(
    deliveryConfigurationContract.updateDeliveryConfiguration,
    async ({ body, request }) => {
      const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
      const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
      const ownerId = partnerId || tenantId;
      if (!ownerId) {
        return BadRequestError.fromError(new Error('Partner ID or Tenant ID is required')).toResponse();
      }
      // get the configuration by id
      const result = await this.deliveryConfigurationService.updateConfiguration(ownerId, body);

      if (!result) {
        return NotFoundError.fromError(
          new Error(`Delivery configuration not found: ${partnerId || tenantId}`),
        ).toResponse();
      }
      const updatedConfig = await this.deliveryConfigurationService.getByOwnerId(ownerId);

      if (!updatedConfig) {
        return NotFoundError.fromError(
          new Error(`Delivery configuration not found after update: ${partnerId || tenantId}`),
        ).toResponse();
      }
      if (updatedConfig.configurationType === 'tenant' && updatedConfig.tenantId) {
        const tenantConfig: TenantDeliveryConfigurationSchema = {
          ...updatedConfig,
          configurationType: 'tenant' as const,
          tenantId: updatedConfig.tenantId,
        };
        return {
          status: 200,
          body: tenantConfig,
        };
      }

      if (updatedConfig.configurationType === 'partner' && updatedConfig.partnerId) {
        const partnerConfig: PartnerDeliveryConfigurationSchema = {
          ...updatedConfig,
          configurationType: 'partner' as const,
          partnerId: updatedConfig.partnerId,
        };
        return {
          status: 200,
          body: partnerConfig,
        };
      }

      throw new Error('Invalid configuration type');
    },
  );

  getByOwnerId = tsRestServer.route(deliveryConfigurationContract.getByOwnerId, async ({ request }) => {
    const partnerId = (request as AuthenticatedRequest).user?.context.partnerId;
    const tenantId = (request as AuthenticatedRequest).user?.context.tenantId;
    const ownerId = partnerId || tenantId;
    if (!ownerId) {
      throw request.server.httpErrors.unauthorized();
    }
    const result = await this.deliveryConfigurationService.getByOwnerId(ownerId);

    if (!result) {
      return NotFoundError.fromError(
        new Error(`Delivery configuration not found: ${partnerId || tenantId}`),
      ).toResponse();
    }

    if (result.configurationType === 'tenant' && result.tenantId) {
      const tenantConfig: TenantDeliveryConfigurationSchema = {
        ...result,
        configurationType: 'tenant' as const,
        tenantId: result.tenantId,
      };
      return {
        status: 200,
        body: tenantConfig,
      };
    }

    if (result.configurationType === 'partner' && result.partnerId) {
      const partnerConfig: PartnerDeliveryConfigurationSchema = {
        ...result,
        configurationType: 'partner' as const,
        partnerId: result.partnerId,
      };
      return {
        status: 200,
        body: partnerConfig,
      };
    }

    throw new Error('Invalid configuration type');
  });
}
