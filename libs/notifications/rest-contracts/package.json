{"name": "@notifications/rest-contracts", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"tslib": "2.8.1"}, "devDependencies": {"@notifications/types": "workspace:*", "@dbd/zod-types-common": "workspace:*"}}